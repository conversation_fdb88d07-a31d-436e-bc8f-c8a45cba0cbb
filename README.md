# Smart Bag Pack - Document Sharing Platform

A secure document sharing platform built with Next.js 15, TypeScript, Tailwind CSS, and Supabase authentication.

## Features

- 🔐 **Complete Authentication System**
  - User registration with email verification
  - Secure login/logout functionality
  - Session management and persistence
  - Protected routes with automatic redirects

- 🛡️ **Security**
  - Server-side authentication checks
  - Middleware-based route protection
  - Secure session handling with Supabase

- 🎨 **Modern UI/UX**
  - Responsive design with Tailwind CSS
  - Clean and intuitive interface
  - Loading states and error handling
  - Form validation with user feedback

- ⚡ **Performance**
  - Next.js 15 with App Router
  - TypeScript for type safety
  - Optimized for production

## Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Authentication**: Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- A Supabase account and project

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd smart_bag_pack
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to find your project URL and anon key
3. Copy `.env.local.example` to `.env.local`:

```bash
cp .env.local.example .env.local
```

4. Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Configure Supabase Authentication

In your Supabase dashboard:

1. Go to Authentication > Settings
2. Add your site URL to "Site URL": `http://localhost:3000`
3. Add redirect URLs:
   - `http://localhost:3000/dashboard`
   - `http://localhost:3000/auth/callback` (for email confirmations)

### 5. Run the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Project Structure

```
smart_bag_pack/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Protected dashboard page
│   ├── login/            # Login page
│   ├── register/         # Registration page
│   ├── layout.tsx        # Root layout with AuthProvider
│   └── page.tsx          # Home/landing page
├── components/            # Reusable components
│   ├── auth/             # Authentication components
│   └── ui/               # UI components
├── contexts/             # React contexts
│   └── AuthContext.tsx   # Authentication context
├── lib/                  # Utility libraries
│   └── supabase/         # Supabase client configuration
├── types/                # TypeScript type definitions
│   └── auth.ts           # Authentication types
├── middleware.ts         # Route protection middleware
└── .env.local           # Environment variables
```

## Authentication Flow

1. **Registration**: Users create an account with email/password
2. **Email Verification**: Supabase sends a confirmation email
3. **Login**: Users sign in with verified credentials
4. **Session Management**: Automatic session handling and refresh
5. **Route Protection**: Middleware protects authenticated routes
6. **Logout**: Secure session cleanup and redirect

## Available Routes

- `/` - Landing page (redirects based on auth status)
- `/login` - User login
- `/register` - User registration
- `/dashboard` - Protected user dashboard

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Your Supabase anonymous key | Yes |

## Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Update Supabase redirect URLs to include your production domain

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Development

### Running Tests

```bash
npm run test
# or
yarn test
```

### Building for Production

```bash
npm run build
npm run start
```

### Linting

```bash
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
