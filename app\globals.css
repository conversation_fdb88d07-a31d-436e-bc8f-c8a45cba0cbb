@import "tailwindcss";

/* Modern Design System Variables */
:root {
  /* Base Colors */
  --background: #ffffff;
  --foreground: #171717;

  /* Glassmorphism Colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-light: rgba(255, 255, 255, 0.15);
  --glass-bg-dark: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-mesh: radial-gradient(at 40% 20%, hsla(28,100%,74%,1) 0px, transparent 50%),
                   radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%),
                   radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%),
                   radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%),
                   radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%),
                   radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%),
                   radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --glass-bg: rgba(0, 0, 0, 0.3);
    --glass-bg-light: rgba(0, 0, 0, 0.4);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Glassmorphism Utilities */
@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-light {
    background: var(--glass-bg-light);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-dark {
    background: var(--glass-bg-dark);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  .gradient-mesh {
    background: var(--gradient-mesh);
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .modern-input {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all duration-300;
  }

  .modern-button {
    @apply px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .modern-button-primary {
    @apply modern-button bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl;
  }

  .modern-button-secondary {
    @apply modern-button glass text-gray-700 hover:bg-white/20 focus:ring-gray-500;
  }

  .modern-card {
    @apply glass rounded-2xl p-6 hover-lift;
  }

  .modern-card-large {
    @apply glass rounded-3xl p-8 hover-lift;
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
