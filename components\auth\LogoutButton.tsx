'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface LogoutButtonProps {
  className?: string
  children?: React.ReactNode
}

export default function LogoutButton({ className = '', children }: LogoutButtonProps) {
  const { signOut } = useAuth()
  const [loading, setLoading] = useState(false)

  const handleSignOut = async () => {
    setLoading(true)
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <button
      onClick={handleSignOut}
      disabled={loading}
      className={`${className} disabled:opacity-50 disabled:cursor-not-allowed transition-colors`}
    >
      {loading ? 'Signing Out...' : (children || 'Sign Out')}
    </button>
  )
}
