import { User } from '@supabase/supabase-js'

export interface AuthUser extends User {
  id: string
  email?: string
  user_metadata?: {
    [key: string]: any
  }
}

export interface AuthState {
  user: AuthUser | null
  loading: boolean
}

export interface AuthContextType extends AuthState {
  signUp: (email: string, password: string) => Promise<{ error: any }>
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: any }>
}

export interface FormData {
  email: string
  password: string
  confirmPassword?: string
}

export interface FormErrors {
  email?: string
  password?: string
  confirmPassword?: string
  general?: string
}
