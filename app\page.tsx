import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Smart Bag Pack</h1>
          <p className="text-lg text-gray-600 mb-8">
            Your secure document sharing platform
          </p>
        </div>

        <div className="bg-white shadow-lg rounded-lg p-8">
          <div className="space-y-4">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
              <svg className="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Welcome to Smart Bag Pack
            </h2>

            <p className="text-gray-600 mb-6">
              Securely share and manage your documents with ease. Get started by creating an account or signing in.
            </p>

            <div className="space-y-3">
              <Link
                href="/register"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium inline-block"
              >
                Create Account
              </Link>

              <Link
                href="/login"
                className="w-full bg-white text-blue-600 py-3 px-4 rounded-md border border-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium inline-block"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-500">
          <p>Secure • Fast • Reliable</p>
        </div>
      </div>
    </div>
  );
}
