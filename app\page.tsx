import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 gradient-mesh"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-blue-400/30 rounded-full blur-xl animate-float"></div>
      <div className="absolute top-40 right-20 w-24 h-24 bg-purple-400/30 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 left-20 w-40 h-40 bg-pink-400/30 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      <div className="absolute bottom-40 right-10 w-28 h-28 bg-indigo-400/30 rounded-full blur-xl animate-float" style={{animationDelay: '1s'}}></div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-lg w-full space-y-8 text-center">
          {/* Header */}
          <div className="space-y-6">
            <div className="animate-float">
              <h1 className="text-6xl sm:text-7xl font-bold text-gradient mb-4">
                Smart Bag Pack
              </h1>
              <div className="h-1 w-24 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </div>

            <p className="text-xl text-gray-700 font-light leading-relaxed">
              Your secure document sharing platform with{' '}
              <span className="text-gradient-accent font-semibold">next-generation</span> technology
            </p>
          </div>

          {/* Main Card */}
          <div className="modern-card-large space-y-8 animate-glow">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-2xl animate-pulse-slow">
              <svg className="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>

            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-gray-800">
                Welcome to the Future
              </h2>

              <p className="text-gray-600 text-lg leading-relaxed">
                Experience seamless document sharing with cutting-edge security,
                intuitive design, and lightning-fast performance.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Link
                href="/register"
                className="w-full modern-button-primary text-lg py-4 shadow-2xl"
              >
                <span className="flex items-center justify-center space-x-2">
                  <span>Create Account</span>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                  </svg>
                </span>
              </Link>

              <Link
                href="/login"
                className="w-full modern-button-secondary text-lg py-4"
              >
                <span className="flex items-center justify-center space-x-2">
                  <span>Sign In</span>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                  </svg>
                </span>
              </Link>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-3 gap-4 mt-12">
            <div className="glass rounded-xl p-4 hover-lift">
              <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Secure</p>
            </div>
            <div className="glass rounded-xl p-4 hover-lift">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Fast</p>
            </div>
            <div className="glass rounded-xl p-4 hover-lift">
              <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Reliable</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
