interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'accent'
  className?: string
}

export default function LoadingSpinner({
  size = 'md',
  variant = 'primary',
  className = ''
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const variantClasses = {
    primary: 'border-blue-600',
    secondary: 'border-purple-600',
    accent: 'border-pink-600'
  }

  return (
    <div className="flex items-center justify-center">
      <div className={`animate-spin rounded-full border-2 border-transparent ${sizeClasses[size]} ${className}`}>
        <div className={`rounded-full border-2 border-t-transparent ${variantClasses[variant]} ${sizeClasses[size]}`}></div>
      </div>
    </div>
  )
}
